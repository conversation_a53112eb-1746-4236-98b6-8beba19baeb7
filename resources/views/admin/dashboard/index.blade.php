@extends('layouts.app')

@section('title', 'Admin Dashboard')

@push('styles')
<link href="{{ asset('css/admin-dashboard.css') }}" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">

        <!-- SMS, Cost Metrics and Balance Summary in Same Row -->
        <div class="row g-3 g-xl-5 mb-3">
            <!-- SMS and Cost Metrics Cards -->
            <x-dashboard.metrics-cards
                :sms-last-week="$aggregatedMetrics['sms_last_week']"
                :cost-last-week="$aggregatedMetrics['cost_last_week']"
                :sms-in-month="$aggregatedMetrics['sms_in_last_month']"
                :cost-in-month="$aggregatedMetrics['cost_in_last_month']"
                :month-name="$aggregatedMetrics['last_month_name']"
            />

            <!-- Balance Summary Component -->
            <x-dashboard.balance-summary
                :balance="$aggregatedMetrics['total_balance']"
                :balance-msgs="$aggregatedMetrics['total_balance_msgs']"
                :show-chart="true"
            />
        </div>

        <!-- Summary Cards Component -->
        <x-dashboard.summary-cards :cards="$summaryCards" />

        <!-- Main Content Row -->
        <div class="row g-5 g-xl-10">
            <!-- Action Queue Component -->
            <x-dashboard.action-queue :action-queue="$actionQueue" />

            <!-- Quick Actions Component -->
            <x-dashboard.quick-actions :actions="$quickActions" />
        </div>
    </div>
    <!--end::Container-->
@endsection

@push('scripts')
<script>
// Auto-refresh dashboard metrics every 60 seconds
setInterval(function() {
    refreshMetrics();
}, 60000);

function refreshMetrics() {
    fetch('{{ route("admin.dashboard.metrics") }}')
        .then(response => response.json())
        .then(data => {
            updateMetricsDisplay(data);
        })
        .catch(error => console.error('Error refreshing metrics:', error));
}

function refreshActionQueue() {
    location.reload(); // Simple refresh for now, can be improved with AJAX
}

function updateMetricsDisplay(metrics) {
    // Update summary cards - can be enhanced to update specific values
    console.log('Metrics updated:', metrics);
}

// Show loading state for quick actions
document.addEventListener('DOMContentLoaded', function() {
    const quickActionButtons = document.querySelectorAll('[data-quick-action]');
    quickActionButtons.forEach(button => {
        button.addEventListener('click', function() {
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
            this.disabled = true;
        });
    });
});
</script>
@endpush
