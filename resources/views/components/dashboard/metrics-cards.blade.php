{{--
    Dashboard Metrics Cards Component
    Displays SMS and cost metrics in card format

    Props:
    - $smsLastWeek: Number of SMS sent last week
    - $costLastWeek: Cost of SMS last week
    - $smsInMonth: Number of SMS sent in specified month
    - $costInMonth: Cost of SMS in specified month
    - $monthName: Name of the month for display
--}}

@props([
    'smsLastWeek' => 0,
    'costLastWeek' => 0,
    'smsInMonth' => 0,
    'costInMonth' => 0,
    'monthName' => 'May'
])

<!--begin::SMS Last Week Card-->
<div class="col-sm-6 col-xl-2 mb-xl-10">
        <!--begin::Card widget 2-->
        <div class="card h-lg-100">
            <!--begin::Body-->
            <div class="card-body d-flex justify-content-between align-items-start flex-column">
                <!--begin::Section-->
                <div class="d-flex flex-column my-7">
                    <!--begin::Number-->
                    <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2">{{ number_format($smsLastWeek) }}</span>
                    <!--end::Number-->
                    <!--begin::Follower-->
                    <div class="m-0">
                        <span class="fw-semibold fs-6 text-gray-400">SMS LAST WEEK</span>
                    </div>
                    <!--end::Follower-->
                </div>
                <!--end::Section-->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Card widget 2-->
    </div>
    <!--end::SMS Last Week Card-->

    <!--begin::Cost Last Week Card-->
    <div class="col-sm-6 col-xl-2 mb-xl-10">
        <!--begin::Card widget 2-->
        <div class="card h-lg-100">
            <!--begin::Body-->
            <div class="card-body d-flex justify-content-between align-items-start flex-column">
                <!--begin::Section-->
                <div class="d-flex flex-column my-7">
                    <!--begin::Number-->
                    <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2">৳{{ number_format($costLastWeek, 2) }}</span>
                    <!--end::Number-->
                    <!--begin::Follower-->
                    <div class="m-0">
                        <span class="fw-semibold fs-6 text-gray-400">COST LAST WEEK</span>
                    </div>
                    <!--end::Follower-->
                </div>
                <!--end::Section-->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Card widget 2-->
    </div>
    <!--end::Cost Last Week Card-->

    <!--begin::SMS In Month Card-->
    <div class="col-sm-6 col-xl-2 mb-xl-10">
        <!--begin::Card widget 2-->
        <div class="card h-lg-100">
            <!--begin::Body-->
            <div class="card-body d-flex justify-content-between align-items-start flex-column">
                <!--begin::Section-->
                <div class="d-flex flex-column my-7">
                    <!--begin::Number-->
                    <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2">{{ number_format($smsInMonth) }}</span>
                    <!--end::Number-->
                    <!--begin::Follower-->
                    <div class="m-0">
                        <span class="fw-semibold fs-6 text-gray-400">SMS IN {{ strtoupper($monthName) }}</span>
                    </div>
                    <!--end::Follower-->
                </div>
                <!--end::Section-->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Card widget 2-->
    </div>
    <!--end::SMS In Month Card-->

    <!--begin::Cost In Month Card-->
    <div class="col-sm-6 col-xl-2 mb-xl-10">
        <!--begin::Card widget 2-->
        <div class="card h-lg-100">
            <!--begin::Body-->
            <div class="card-body d-flex justify-content-between align-items-start flex-column">
                <!--begin::Section-->
                <div class="d-flex flex-column my-7">
                    <!--begin::Number-->
                    <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2">৳{{ number_format($costInMonth, 2) }}</span>
                    <!--end::Number-->
                    <!--begin::Follower-->
                    <div class="m-0">
                        <span class="fw-semibold fs-6 text-gray-400">COST IN {{ strtoupper($monthName) }}</span>
                    </div>
                    <!--end::Follower-->
                </div>
                <!--end::Section-->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Card widget 2-->
    </div>
    <!--end::Cost In Month Card-->
