{{--
    Dashboard Balance Summary Component
    Displays balance information in a purple gradient card

    Props:
    - $balance: Current balance amount
    - $balanceMsgs: Balance converted to message count
    - $showChart: Whether to show the chart (default: true)
--}}

@props([
    'balance' => 0,
    'balanceMsgs' => 0,
    'showChart' => true
])

<!--begin::Col-->
<div class="col-xl-4 mb-xl-10">
    <!--begin::Card widget 1-->
    <div class="card card-flush border-0 h-lg-100" data-bs-theme="light" style="background-color: #7239EA">
        <!--begin::Header-->
        <div class="card-header pt-2">
            <!--begin::Title-->
            <h3 class="card-title">
                <span class="text-white fs-3 fw-bold me-2">Balance Summary</span>
            </h3>
            <!--end::Title-->
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body d-flex justify-content-between flex-column pt-1 px-0 pb-0">
            <!--begin::Wrapper-->
            <div class="d-flex flex-wrap px-9 mb-5">
                <!--begin::Balance Stat-->
                <div class="rounded min-w-125px py-3 px-4 my-1 me-6" style="border: 1px dashed rgba(255, 255, 255, 0.2)">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <div class="text-white fs-2 fw-bold">৳{{ number_format($balance, 2) }}</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <div class="fw-semibold fs-6 text-white opacity-50">Balance</div>
                    <!--end::Label-->
                </div>
                <!--end::Balance Stat-->
                <!--begin::Messages Stat-->
                <div class="rounded min-w-125px py-3 px-4 my-1" style="border: 1px dashed rgba(255, 255, 255, 0.2)">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <div class="text-white fs-2 fw-bold" data-kt-countup="true" data-kt-countup-value="{{ $balanceMsgs }}">{{ number_format($balanceMsgs) }}</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <div class="fw-semibold fs-6 text-white opacity-50">Balance (MSGS)</div>
                    <!--end::Label-->
                </div>
                <!--end::Messages Stat-->
            </div>
            <!--end::Wrapper-->
            @if($showChart)
                <!--begin::Chart-->
                <div id="kt_card_widget_1_chart" data-kt-chart-color="#8F5FF4" style="height: 105px"></div>
                <!--end::Chart-->
            @endif
        </div>
        <!--end::Body-->
    </div>
    <!--end::Card widget 1-->
</div>
<!--end::Col-->
